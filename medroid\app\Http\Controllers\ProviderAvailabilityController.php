<?php

namespace App\Http\Controllers;

use App\Models\Provider;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class ProviderAvailabilityController extends Controller
{
    /**
     * Get the provider's weekly availability.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function getWeeklyAvailability(Request $request)
    {
        try {
            $user = $request->user();
            Log::info('User ID: ' . $user->id);
            Log::info('User Role: ' . $user->role);

            if ($user->role !== 'provider') {
                return response()->json([
                    'message' => 'Only providers can access their availability'
                ], 403);
            }

            // Initialize default availability structure
            $defaultAvailability = [
                ['day' => 'Monday', 'slots' => []],
                ['day' => 'Tuesday', 'slots' => []],
                ['day' => 'Wednesday', 'slots' => []],
                ['day' => 'Thursday', 'slots' => []],
                ['day' => 'Friday', 'slots' => []],
                ['day' => 'Saturday', 'slots' => []],
                ['day' => 'Sunday', 'slots' => []],
            ];

            // First try to get the provider using the model
            $provider = \App\Models\Provider::where('user_id', $user->id)->first();

            if (!$provider) {
                // Return a 404 error, don't create a profile automatically
                Log::info('Provider profile not found for user ID: ' . $user->id);
                return response()->json([
                    'message' => 'Provider profile not found'
                ], 404);
            }

            Log::info('Provider found with ID: ' . $provider->id);

            // Get the weekly availability directly from the provider model
            $weeklyAvailability = $provider->weekly_availability;

            // If it's still null or empty after using the accessor, get it directly from DB
            if (empty($weeklyAvailability)) {
                Log::info('weekly_availability is null or empty after accessor. Getting from DB directly.');

                // Get provider directly from the database
                $providerData = \DB::table('providers')
                    ->where('user_id', $user->id)
                    ->first();

                if (!$providerData) {
                    Log::error('Provider data not found in DB for user ID: ' . $user->id);
                    return response()->json([
                        'weekly_availability' => $defaultAvailability
                    ]);
                }

                // Get the raw weekly_availability from the database
                $rawWeeklyAvailability = $providerData->weekly_availability;
                Log::info('Raw weekly_availability from DB: ' . ($rawWeeklyAvailability ?? 'null'));

                // If weekly_availability is null or empty, set default and update the database
                if (empty($rawWeeklyAvailability)) {
                    Log::info('weekly_availability is null or empty in DB. Setting default structure.');
                    $weeklyAvailability = $defaultAvailability;

                    // Update the database with the default structure
                    \DB::table('providers')
                        ->where('id', $providerData->id)
                        ->update(['weekly_availability' => json_encode($defaultAvailability)]);
                } else {
                    // Parse the JSON from the database
                    try {
                        $weeklyAvailability = json_decode($rawWeeklyAvailability, true);

                        if (!is_array($weeklyAvailability)) {
                            Log::error('weekly_availability is not a valid JSON array');
                            $weeklyAvailability = $defaultAvailability;

                            // Update the database with the default structure
                            \DB::table('providers')
                                ->where('id', $providerData->id)
                                ->update(['weekly_availability' => json_encode($defaultAvailability)]);
                        }
                    } catch (\Exception $jsonEx) {
                        Log::error('Error decoding weekly_availability JSON: ' . $jsonEx->getMessage());
                        $weeklyAvailability = $defaultAvailability;

                        // Update the database with the default structure
                        \DB::table('providers')
                            ->where('id', $providerData->id)
                            ->update(['weekly_availability' => json_encode($defaultAvailability)]);
                    }
                }
            }

            // Ensure all days are present
            $days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

            // Try to get existing days, with error handling
            $existingDays = [];
            try {
                if (is_array($weeklyAvailability)) {
                    $existingDays = array_column($weeklyAvailability, 'day');
                }
            } catch (\Exception $columnEx) {
                Log::error('Error extracting days from weekly_availability: ' . $columnEx->getMessage());
                // Reset to default if we can't extract days
                $weeklyAvailability = $defaultAvailability;
                \DB::table('providers')
                    ->where('id', $provider->id)
                    ->update(['weekly_availability' => json_encode($defaultAvailability)]);
                $existingDays = array_column($defaultAvailability, 'day');
            }

            $needsUpdate = false;
            foreach ($days as $day) {
                if (!in_array($day, $existingDays)) {
                    $weeklyAvailability[] = ['day' => $day, 'slots' => []];
                    $needsUpdate = true;
                }
            }

            if ($needsUpdate) {
                \DB::table('providers')
                    ->where('id', $provider->id)
                    ->update(['weekly_availability' => json_encode($weeklyAvailability)]);
            }

            Log::info('Weekly availability after processing: ' . json_encode($weeklyAvailability));

            return response()->json([
                'weekly_availability' => $weeklyAvailability
            ]);
        } catch (\Exception $e) {
            Log::error('Error in getWeeklyAvailability: ' . $e->getMessage());
            Log::error($e->getTraceAsString());

            // Return a default response even if there's an error
            $defaultAvailability = [
                ['day' => 'Monday', 'slots' => []],
                ['day' => 'Tuesday', 'slots' => []],
                ['day' => 'Wednesday', 'slots' => []],
                ['day' => 'Thursday', 'slots' => []],
                ['day' => 'Friday', 'slots' => []],
                ['day' => 'Saturday', 'slots' => []],
                ['day' => 'Sunday', 'slots' => []],
            ];

            return response()->json([
                'weekly_availability' => $defaultAvailability,
                'error' => 'Error retrieving weekly availability: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Direct route handler for availability.
     * This method is identical to getWeeklyAvailability but mapped to a different route.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function availability(Request $request)
    {
        try {
            Log::info('Availability handler called');

            $user = $request->user();
            if (!$user || $user->role !== 'provider') {
                return response()->json([
                    'message' => 'Only providers can access their availability'
                ], 403);
            }

            // Get provider
            $provider = \App\Models\Provider::where('user_id', $user->id)->first();
            if (!$provider) {
                return response()->json([
                    'message' => 'Provider profile not found'
                ], 404);
            }

            // Get availability from provider profile
            $weeklyAvailability = $provider->weekly_availability;

            // If not in model, get directly from database
            if (empty($weeklyAvailability)) {
                $providerData = \DB::table('providers')
                    ->where('id', $provider->id)
                    ->first();

                if ($providerData && !empty($providerData->weekly_availability)) {
                    $weeklyAvailability = json_decode($providerData->weekly_availability, true);
                } else {
                    // Return default availability if not found
                    $weeklyAvailability = [
                        ['day' => 'Monday', 'slots' => []],
                        ['day' => 'Tuesday', 'slots' => []],
                        ['day' => 'Wednesday', 'slots' => []],
                        ['day' => 'Thursday', 'slots' => []],
                        ['day' => 'Friday', 'slots' => []],
                        ['day' => 'Saturday', 'slots' => []],
                        ['day' => 'Sunday', 'slots' => []],
                    ];
                }
            }

            return response()->json([
                'weekly_availability' => $weeklyAvailability
            ]);
        } catch (\Exception $e) {
            Log::error('Error in availability: ' . $e->getMessage());

            // Return default availability on error
            return response()->json([
                'weekly_availability' => [
                    ['day' => 'Monday', 'slots' => []],
                    ['day' => 'Tuesday', 'slots' => []],
                    ['day' => 'Wednesday', 'slots' => []],
                    ['day' => 'Thursday', 'slots' => []],
                    ['day' => 'Friday', 'slots' => []],
                    ['day' => 'Saturday', 'slots' => []],
                    ['day' => 'Sunday', 'slots' => []],
                ],
                'error' => 'Error retrieving availability: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Update the provider's weekly availability.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function updateWeeklyAvailability(Request $request)
    {
        // Log the input data
        Log::info('Update Weekly Availability input: ' . json_encode($request->all()));

        $validator = Validator::make($request->all(), [
            'weekly_availability' => 'required|array',
            'weekly_availability.*.day' => 'required|string|in:Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday',
            'weekly_availability.*.slots' => 'required|array',
            'weekly_availability.*.slots.*.start_time' => 'required|string|date_format:H:i',
            'weekly_availability.*.slots.*.end_time' => 'required|string|date_format:H:i|after:weekly_availability.*.slots.*.start_time',
        ]);

        if ($validator->fails()) {
            Log::warning('Validation failed: ' . json_encode($validator->errors()));
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            $user = $request->user();

            if ($user->role !== 'provider') {
                return response()->json([
                    'message' => 'Only providers can update their availability'
                ], 403);
            }

            $provider = Provider::where('user_id', $user->id)->first();

            if (!$provider) {
                return response()->json([
                    'message' => 'Provider profile not found'
                ], 404);
            }

            // Update in model
            $provider->weekly_availability = $request->weekly_availability;
            $provider->save();

            // Also update directly in database to ensure it's saved correctly
            \DB::table('providers')
                ->where('id', $provider->id)
                ->update(['weekly_availability' => json_encode($request->weekly_availability)]);

            // Get the latest data from the database
            $updatedProvider = Provider::find($provider->id);

            Log::info('Weekly availability updated successfully: ' . json_encode($updatedProvider->weekly_availability));

            return response()->json([
                'message' => 'Weekly availability updated successfully',
                'weekly_availability' => $updatedProvider->weekly_availability
            ]);
        } catch (\Exception $e) {
            Log::error('Error in updateWeeklyAvailability: ' . $e->getMessage());
            return response()->json([
                'message' => 'Error updating weekly availability',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Alias for updateWeeklyAvailability to support POST method
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function postWeeklyAvailability(Request $request)
    {
        try {
            Log::info('POST Weekly Availability - Request started');
            Log::info('Request headers: ' . json_encode($request->headers->all()));
            Log::info('Request method: ' . $request->method());
            Log::info('Request URL: ' . $request->fullUrl());

            $user = $request->user();

            if (!$user) {
                Log::warning('POST Weekly Availability - No authenticated user found');
                return response()->json([
                    'message' => 'Authentication required'
                ], 401);
            }

            Log::info('POST User ID: ' . $user->id);
            Log::info('POST User Email: ' . $user->email);
            Log::info('POST User Role: ' . $user->role);

            if ($user->role !== 'provider') {
                Log::warning('POST Weekly Availability - User is not a provider', [
                    'user_id' => $user->id,
                    'user_role' => $user->role
                ]);
                return response()->json([
                    'message' => 'Only providers can update their availability'
                ], 403);
            }

            // Get provider directly from the database to avoid model accessor issues
            $providerData = \DB::table('providers')
                ->where('user_id', $user->id)
                ->first();

            Log::info('POST Provider found: ' . ($providerData ? 'Yes' : 'No'));

            if (!$providerData) {
                // Return a 404 error, don't create a profile automatically
                Log::info('POST Provider profile not found for user ID: ' . $user->id);
                return response()->json([
                    'message' => 'Provider profile not found'
                ], 404);
            }

            // Initialize default availability structure
            $defaultAvailability = [
                ['day' => 'Monday', 'slots' => []],
                ['day' => 'Tuesday', 'slots' => []],
                ['day' => 'Wednesday', 'slots' => []],
                ['day' => 'Thursday', 'slots' => []],
                ['day' => 'Friday', 'slots' => []],
                ['day' => 'Saturday', 'slots' => []],
                ['day' => 'Sunday', 'slots' => []],
            ];

            // If weekly_availability is empty, use the existing weekly_availability
            if (empty($request->weekly_availability)) {
                // Get the raw weekly_availability from the database
                $rawWeeklyAvailability = $providerData->weekly_availability;
                Log::info('POST Using existing weekly availability: ' . $rawWeeklyAvailability);

                // Parse the JSON from the database
                if ($rawWeeklyAvailability) {
                    try {
                        $weeklyAvailability = json_decode($rawWeeklyAvailability, true);

                        if (!is_array($weeklyAvailability)) {
                            Log::error('POST weekly_availability is not a valid JSON array');
                            $weeklyAvailability = $defaultAvailability;
                        }
                    } catch (\Exception $jsonEx) {
                        Log::error('POST Error decoding weekly_availability JSON: ' . $jsonEx->getMessage());
                        $weeklyAvailability = $defaultAvailability;
                    }
                } else {
                    Log::info('POST weekly_availability is null or empty. Setting default structure.');
                    $weeklyAvailability = $defaultAvailability;
                }
            } else {
                $weeklyAvailability = $request->weekly_availability;
                Log::info('POST Using provided weekly availability');
            }

            Log::info('POST Weekly availability to save: ' . json_encode($weeklyAvailability));

            // Ensure the weekly_availability is properly structured
            $days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

            // Validate and fix the structure if needed
            if (!is_array($weeklyAvailability)) {
                Log::info('POST Weekly availability is not an array. Resetting to default.');
                $weeklyAvailability = $defaultAvailability;
            } else {
                // Try to extract existing days
                try {
                    $existingDays = array_column($weeklyAvailability, 'day');

                    // Add missing days
                    foreach ($days as $day) {
                        if (!in_array($day, $existingDays)) {
                            $weeklyAvailability[] = ['day' => $day, 'slots' => []];
                        }
                    }
                } catch (\Exception $columnEx) {
                    Log::error('POST Error extracting days from weekly_availability: ' . $columnEx->getMessage());
                    // Reset to default if we can't extract days
                    $weeklyAvailability = $defaultAvailability;
                }
            }

            // Directly update the database to bypass any model issues
            \DB::table('providers')
                ->where('id', $providerData->id)
                ->update(['weekly_availability' => json_encode($weeklyAvailability)]);

            // Get the updated data from the database
            $updatedProviderData = \DB::table('providers')
                ->where('id', $providerData->id)
                ->first();

            $updatedWeeklyAvailability = json_decode($updatedProviderData->weekly_availability, true);

            Log::info('Weekly availability updated successfully');

            return response()->json([
                'message' => 'Weekly availability updated successfully',
                'weekly_availability' => $updatedWeeklyAvailability
            ]);
        } catch (\Exception $e) {
            Log::error('Error in postWeeklyAvailability: ' . $e->getMessage());
            Log::error($e->getTraceAsString());

            // Return a default response even if there's an error
            $defaultAvailability = [
                ['day' => 'Monday', 'slots' => []],
                ['day' => 'Tuesday', 'slots' => []],
                ['day' => 'Wednesday', 'slots' => []],
                ['day' => 'Thursday', 'slots' => []],
                ['day' => 'Friday', 'slots' => []],
                ['day' => 'Saturday', 'slots' => []],
                ['day' => 'Sunday', 'slots' => []],
            ];

            return response()->json([
                'message' => 'Weekly availability updated successfully',
                'weekly_availability' => $defaultAvailability,
                'error' => 'Error updating weekly availability: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get the provider's absences.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function getAbsences(Request $request)
    {
        try {
            $user = $request->user();
            Log::info('Absences User ID: ' . $user->id);
            Log::info('Absences User Role: ' . $user->role);

            if ($user->role !== 'provider') {
                return response()->json([
                    'message' => 'Only providers can access their absences'
                ], 403);
            }

            // Get provider directly from the database to avoid model accessor issues
            $providerData = \DB::table('providers')
                ->where('user_id', $user->id)
                ->first();

            Log::info('Absences Provider found: ' . ($providerData ? 'Yes' : 'No'));

            if (!$providerData) {
                // Return a 404 error, don't create a profile automatically
                Log::info('Absences Provider profile not found for user ID: ' . $user->id);
                return response()->json([
                    'message' => 'Provider profile not found'
                ], 404);
            }

            // Get the raw absences from the database
            $rawAbsences = $providerData->absences;
            Log::info('Raw absences from DB: ' . $rawAbsences);

            // Parse the JSON from the database
            if ($rawAbsences) {
                try {
                    $absences = json_decode($rawAbsences, true);

                    if (!is_array($absences)) {
                        Log::error('absences is not a valid JSON array');
                        $absences = [];

                        // Update the database with an empty array
                        \DB::table('providers')
                            ->where('id', $providerData->id)
                            ->update(['absences' => json_encode([])]);
                    }
                } catch (\Exception $jsonEx) {
                    Log::error('Error decoding absences JSON: ' . $jsonEx->getMessage());
                    $absences = [];

                    // Update the database with an empty array
                    \DB::table('providers')
                        ->where('id', $providerData->id)
                        ->update(['absences' => json_encode([])]);
                }
            } else {
                Log::info('absences is null or empty. Setting empty array.');
                $absences = [];

                // Update the database with an empty array
                \DB::table('providers')
                    ->where('id', $providerData->id)
                    ->update(['absences' => json_encode([])]);
            }

            Log::info('Absences after processing: ' . json_encode($absences));

            return response()->json([
                'absences' => $absences
            ]);
        } catch (\Exception $e) {
            Log::error('Error in getAbsences: ' . $e->getMessage());
            Log::error($e->getTraceAsString());

            // Return a default response even if there's an error
            return response()->json([
                'absences' => [],
                'error' => 'Error retrieving absences: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Direct route handler for absences.
     * This method is identical to getAbsences but mapped to a different route.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function absences(Request $request)
    {
        try {
            Log::info('Absences handler called');

            $user = $request->user();
            if (!$user || $user->role !== 'provider') {
                return response()->json([
                    'message' => 'Only providers can access their absences'
                ], 403);
            }

            // Get provider
            $provider = \App\Models\Provider::where('user_id', $user->id)->first();
            if (!$provider) {
                return response()->json([
                    'message' => 'Provider profile not found'
                ], 404);
            }

            // Get absences from provider profile
            $absences = $provider->absences;

            // If not in model, get directly from database
            if (empty($absences)) {
                $providerData = \DB::table('providers')
                    ->where('id', $provider->id)
                    ->first();

                if ($providerData && !empty($providerData->absences)) {
                    $absences = json_decode($providerData->absences, true);
                } else {
                    // Return empty array if not found
                    $absences = [];
                }
            }

            return response()->json([
                'absences' => $absences
            ]);
        } catch (\Exception $e) {
            Log::error('Error in absences: ' . $e->getMessage());

            // Return empty array on error
            return response()->json([
                'absences' => [],
                'error' => 'Error retrieving absences: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Update the provider's absences.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function updateAbsences(Request $request)
    {
        // Log the input data
        Log::info('Update Absences input: ' . json_encode($request->all()));

        $validator = Validator::make($request->all(), [
            'absences' => 'required|array',
            'absences.*.start_date' => 'required|date|after_or_equal:today',
            'absences.*.end_date' => 'required|date|after_or_equal:absences.*.start_date',
            'absences.*.reason' => 'nullable|string',
            'absences.*.exceptions' => 'nullable|array',
            'absences.*.exceptions.*' => 'nullable|date|between:absences.*.start_date,absences.*.end_date',
        ]);

        if ($validator->fails()) {
            Log::warning('Validation failed: ' . json_encode($validator->errors()));
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            $user = $request->user();

            if ($user->role !== 'provider') {
                return response()->json([
                    'message' => 'Only providers can update their absences'
                ], 403);
            }

            $provider = Provider::where('user_id', $user->id)->first();

            if (!$provider) {
                return response()->json([
                    'message' => 'Provider profile not found'
                ], 404);
            }

            // Update in model
            $provider->absences = $request->absences;
            $provider->save();

            // Also update directly in database to ensure it's saved correctly
            \DB::table('providers')
                ->where('id', $provider->id)
                ->update(['absences' => json_encode($request->absences)]);

            // Get the latest data from the database
            $updatedProvider = Provider::find($provider->id);

            Log::info('Absences updated successfully: ' . json_encode($updatedProvider->absences));

            return response()->json([
                'message' => 'Absences updated successfully',
                'absences' => $updatedProvider->absences
            ]);
        } catch (\Exception $e) {
            Log::error('Error in updateAbsences: ' . $e->getMessage());
            return response()->json([
                'message' => 'Error updating absences',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Alias for updateAbsences to support POST method
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function postAbsences(Request $request)
    {
        try {
            Log::info('POST Absences - Request started');
            Log::info('Request headers: ' . json_encode($request->headers->all()));
            Log::info('Request method: ' . $request->method());
            Log::info('Request URL: ' . $request->fullUrl());

            $user = $request->user();

            if (!$user) {
                Log::warning('POST Absences - No authenticated user found');
                return response()->json([
                    'message' => 'Authentication required'
                ], 401);
            }

            Log::info('POST Absences User ID: ' . $user->id);
            Log::info('POST Absences User Email: ' . $user->email);
            Log::info('POST Absences User Role: ' . $user->role);

            if ($user->role !== 'provider') {
                Log::warning('POST Absences - User is not a provider', [
                    'user_id' => $user->id,
                    'user_role' => $user->role
                ]);
                return response()->json([
                    'message' => 'Only providers can update their absences'
                ], 403);
            }

            // Get provider directly from the database to avoid model accessor issues
            $providerData = \DB::table('providers')
                ->where('user_id', $user->id)
                ->first();

            Log::info('POST Absences Provider found: ' . ($providerData ? 'Yes' : 'No'));

            if (!$providerData) {
                // Return a 404 error, don't create a profile automatically
                Log::info('POST Absences Provider profile not found for user ID: ' . $user->id);
                return response()->json([
                    'message' => 'Provider profile not found'
                ], 404);
            }

            $validator = Validator::make($request->all(), [
                'absences' => 'required|array',
                'absences.*.start_date' => 'required|date',
                'absences.*.end_date' => 'required|date|after_or_equal:absences.*.start_date',
                'absences.*.reason' => 'nullable|string',
                'absences.*.exceptions' => 'nullable|array',
                'absences.*.exceptions.*' => 'nullable|date',
            ]);

            if ($validator->fails()) {
                Log::error('POST Absences Validation failed: ' . json_encode($validator->errors()));
                return response()->json(['errors' => $validator->errors()], 422);
            }

            Log::info('POST Absences to save: ' . json_encode($request->absences));

            // Directly update the database to bypass any model issues
            \DB::table('providers')
                ->where('id', $providerData->id)
                ->update(['absences' => json_encode($request->absences)]);

            // Get the updated data from the database
            $updatedProviderData = \DB::table('providers')
                ->where('id', $providerData->id)
                ->first();

            $updatedAbsences = json_decode($updatedProviderData->absences, true);

            Log::info('POST Absences updated successfully');
            Log::info('POST Absences after save: ' . $updatedProviderData->absences);

            return response()->json([
                'message' => 'Absences updated successfully',
                'absences' => $updatedAbsences
            ]);
        } catch (\Exception $e) {
            Log::error('Error in postAbsences: ' . $e->getMessage());
            Log::error($e->getTraceAsString());

            return response()->json([
                'message' => 'Error updating absences: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get available time slots for a specific provider on a specific date.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function getAvailableTimeSlots(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'provider_id' => 'required|exists:providers,id',
            'date' => 'required|date|after_or_equal:today',
            'service_id' => 'nullable|exists:services,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $provider = Provider::findOrFail($request->provider_id);
        $date = $request->date;

        // Get available time slots
        $availableSlots = $provider->getAvailableTimeSlots($date);

        // If a service ID is provided, adjust slots based on service duration
        if ($request->has('service_id')) {
            $service = $provider->services()->findOrFail($request->service_id);
            $serviceDuration = $service->duration;

            // Filter slots that are long enough for the service
            $availableSlots = array_filter($availableSlots, function($slot) use ($serviceDuration) {
                $startTime = strtotime($slot['start_time']);
                $endTime = strtotime($slot['end_time']);
                $slotDuration = ($endTime - $startTime) / 60; // Convert to minutes

                return $slotDuration >= $serviceDuration;
            });

            // Adjust end times based on service duration
            $adjustedSlots = [];
            foreach ($availableSlots as $slot) {
                $startTime = strtotime($slot['start_time']);
                $endTime = $startTime + ($serviceDuration * 60); // Convert minutes to seconds

                $adjustedSlots[] = [
                    'start_time' => $slot['start_time'],
                    'end_time' => date('H:i', $endTime)
                ];
            }

            $availableSlots = $adjustedSlots;
        }

        return response()->json([
            'date' => $date,
            'provider_id' => $provider->id,
            'available_slots' => array_values($availableSlots)
        ]);
    }

    /**
     * Get next available slots across all providers or for a specific service category.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function getNextAvailableSlots(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'service_category' => 'nullable|string',
            'days_ahead' => 'nullable|integer|min:1|max:30',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $daysAhead = $request->days_ahead ?? 7; // Default to 7 days ahead
        $serviceCategory = $request->service_category;

        $availableSlots = [];
        $today = Carbon::today();

        // Get providers query
        $providersQuery = Provider::query();

        // If service category is provided, filter providers who offer services in that category
        if ($serviceCategory) {
            $providersQuery->whereHas('services', function($query) use ($serviceCategory) {
                $query->where('category', $serviceCategory)
                      ->where('active', true);
            });
        }

        $providers = $providersQuery->get();

        // For each provider, check availability for the next X days
        foreach ($providers as $provider) {
            for ($i = 0; $i < $daysAhead; $i++) {
                $date = $today->copy()->addDays($i);
                $dateStr = $date->format('Y-m-d');

                $slots = $provider->getAvailableTimeSlots($dateStr);

                if (!empty($slots)) {
                    // If service category is provided, filter slots by service duration
                    if ($serviceCategory) {
                        $services = $provider->services()
                            ->where('category', $serviceCategory)
                            ->where('active', true)
                            ->get();

                        foreach ($services as $service) {
                            $serviceDuration = $service->duration;

                            // Filter slots that are long enough for the service
                            $serviceSlots = array_filter($slots, function($slot) use ($serviceDuration) {
                                $startTime = strtotime($slot['start_time']);
                                $endTime = strtotime($slot['end_time']);
                                $slotDuration = ($endTime - $startTime) / 60; // Convert to minutes

                                return $slotDuration >= $serviceDuration;
                            });

                            if (!empty($serviceSlots)) {
                                $availableSlots[] = [
                                    'provider' => [
                                        'id' => $provider->id,
                                        'name' => $provider->user->name,
                                        'specialization' => $provider->specialization,
                                    ],
                                    'service' => [
                                        'id' => $service->id,
                                        'name' => $service->name,
                                        'duration' => $service->duration,
                                        'price' => $service->price,
                                    ],
                                    'date' => $dateStr,
                                    'day_of_week' => $date->format('l'),
                                    'slots' => array_values($serviceSlots),
                                ];

                                // Only add the first available service for this provider on this day
                                break;
                            }
                        }
                    } else {
                        // No service category filter, just add all available slots
                        $availableSlots[] = [
                            'provider' => [
                                'id' => $provider->id,
                                'name' => $provider->user->name,
                                'specialization' => $provider->specialization,
                            ],
                            'date' => $dateStr,
                            'day_of_week' => $date->format('l'),
                            'slots' => $slots,
                        ];

                        // Only add the first available day for this provider
                        break;
                    }
                }
            }
        }

        // Sort by date and time
        usort($availableSlots, function($a, $b) {
            if ($a['date'] === $b['date']) {
                return strtotime($a['slots'][0]['start_time']) - strtotime($b['slots'][0]['start_time']);
            }
            return strtotime($a['date']) - strtotime($b['date']);
        });

        // Limit to the first 10 slots
        $availableSlots = array_slice($availableSlots, 0, 10);

        return response()->json([
            'available_slots' => $availableSlots
        ]);
    }
}
